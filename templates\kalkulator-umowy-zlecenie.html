{% extends 'base.html' %}

{% block head %}
<!-- Chart.js for salary breakdown visualization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block title %}Kalkulator Umowy Zlecenie 2025 | Oblicz Wynagrodzenie Zlecenie{% endblock %}

{% block description %}✅ Kalkulator umowy zlecenie 2025 - oblicz wynagrodzenie brutto netto, składki <PERSON>, podatek. Darmowy kalkulator dla umów zlecenie. Sprawdź teraz!{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active" aria-current="page">Kalkulator Umowy Zlecenie</li>
{% endblock %}

{% block schema_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": ["WebApplication", "Calculator"],
  "name": "Kalkulator Umowy Zlecenie 2025",
  "url": "{{ request.url_root }}kalkulator-umowy-zlecenie",
  "description": "Darmowy kalkulator umowy zlecenie 2025 - oblicz wynagrodzenie brutto netto, składki ZUS, podatek dla umów zlecenie",
  "applicationCategory": "FinanceApplication",
  "operatingSystem": "All",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "PLN"
  },
  "author": {
    "@type": "Organization",
    "name": "Kalkulator Wynagrodzeń",
    "url": "{{ request.url_root }}"
  },
  "potentialAction": {
    "@type": "UseAction",
    "target": "{{ request.url_root }}kalkulator-umowy-zlecenie#kalkulator",
    "object": {
      "@type": "WebApplication",
      "name": "Kalkulator Umowy Zlecenie"
    }
  }
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "Jakie składki ZUS płaci się od umowy zlecenie?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Od umowy zlecenie płaci się składki emerytalne (9,76%), rentowe (1,5%) oraz wypadkowe (1,67%). Składki chorobowe są dobrowolne."
      }
    },
    {
      "@type": "Question", 
      "name": "Czy od umowy zlecenie płaci się podatek dochodowy?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Tak, od umowy zlecenie płaci się podatek dochodowy według skali podatkowej 12% lub 32% w zależności od wysokości rocznych dochodów."
      }
    },
    {
      "@type": "Question",
      "name": "Jaka jest minimalna kwota umowy zlecenie w 2025?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Minimalna kwota umowy zlecenie w 2025 roku wynosi 4666 PLN brutto miesięcznie, jeśli jest to główne źródło dochodu."
      }
    }
  ]
}
</script>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section bg-primary text-white text-center py-5" id="hero">
  <div class="container">
    <h1 class="display-4 fw-bold mb-4">Kalkulator Umowy Zlecenie 2025 - Oblicz Wynagrodzenie</h1>
    <p class="lead mb-5">Darmowy kalkulator umowy zlecenie 2025 - oblicz wynagrodzenie brutto netto, składki ZUS i podatek dochodowy dla umów zlecenie</p>
    <div class="d-flex justify-content-center gap-3 flex-wrap">
      <a href="#kalkulator" class="btn btn-light btn-lg px-4 py-2">
        <i class="fas fa-calculator me-2"></i>Oblicz Umowę Zlecenie
      </a>
      <a href="#poradnik" class="btn btn-outline-light btn-lg px-4 py-2">
        <i class="fas fa-book me-2"></i>Jak Działa
      </a>
    </div>
  </div>
</section>

<!-- Kalkulator Umowy Zlecenie Section -->
<section id="kalkulator" class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Kalkulator Umowy Zlecenie 2025</h2>
      <p class="lead">Nasz <strong>kalkulator umowy zlecenie</strong> pozwala szybko obliczyć wynagrodzenie brutto netto dla umów zlecenie w 2025 roku z uwzględnieniem aktualnych składek ZUS i podatku dochodowego.</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="card shadow-sm">
          <div class="card-body p-4">
            <form id="mandate-calculator">
              <div class="row g-3 mb-4">
                <div class="col-md-6">
                  <label for="mandate-amount" class="form-label fw-bold">Kwota wynagrodzenia</label>
                  <div class="input-group">
                    <input type="number" id="mandate-amount" class="form-control" placeholder="Wprowadź kwotę" min="0" step="0.01" required>
                    <span class="input-group-text">PLN</span>
                  </div>
                </div>

                <div class="col-md-6">
                  <label class="form-label fw-bold">Typ kwoty</label>
                  <div class="d-flex gap-3 mt-2">
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="mandate-amount-type" id="mandate-gross" value="gross" checked>
                      <label class="form-check-label" for="mandate-gross">Brutto</label>
                    </div>
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="mandate-amount-type" id="mandate-net" value="net">
                      <label class="form-check-label" for="mandate-net">Netto</label>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row g-3 mb-4">
                <div class="col-md-4">
                  <label for="mandate-age" class="form-label fw-bold">Wiek</label>
                  <div class="input-group">
                    <input type="number" id="mandate-age" class="form-control" placeholder="26" min="18" max="100" value="26">
                    <span class="input-group-text">lat</span>
                  </div>
                </div>

                <div class="col-md-4">
                  <div class="form-check mt-4">
                    <input class="form-check-input" type="checkbox" id="mandate-main-job" value="main-job" checked>
                    <label class="form-check-label fw-bold" for="mandate-main-job">
                      Główne źródło dochodu
                    </label>
                  </div>
                </div>

                <div class="col-md-4">
                  <div class="form-check mt-4">
                    <input class="form-check-input" type="checkbox" id="mandate-health-insurance" value="health-insurance">
                    <label class="form-check-label fw-bold" for="mandate-health-insurance">
                      Dobrowolne ubezpieczenie chorobowe
                    </label>
                  </div>
                </div>
              </div>

              <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg px-5 py-3">
                  <i class="fas fa-calculator me-2"></i>OBLICZ UMOWĘ ZLECENIE
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Results Section -->
        <div id="mandate-results" class="mt-4" style="display: none;">
          <div class="card border-success">
            <div class="card-header bg-success text-white">
              <h4 class="mb-0"><i class="fas fa-chart-line me-2"></i>Wyniki Kalkulacji Umowy Zlecenie</h4>
            </div>
            <div class="card-body">
              <div class="row g-4">
                <div class="col-md-6">
                  <div class="result-item p-3 bg-light rounded">
                    <h5 class="text-primary mb-2">Wynagrodzenie brutto</h5>
                    <div class="h3 text-dark mb-0"><span id="mandate-gross-result">--</span> PLN</div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="result-item p-3 bg-light rounded">
                    <h5 class="text-success mb-2">Wynagrodzenie netto</h5>
                    <div class="h3 text-dark mb-0"><span id="mandate-net-result">--</span> PLN</div>
                  </div>
                </div>
              </div>

              <div class="row g-4 mt-2">
                <div class="col-md-4">
                  <div class="result-item p-3 border rounded">
                    <h6 class="text-muted mb-2">Składki ZUS</h6>
                    <div class="h5 mb-0"><span id="mandate-zus-result">--</span> PLN</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="result-item p-3 border rounded">
                    <h6 class="text-muted mb-2">Podatek dochodowy</h6>
                    <div class="h5 mb-0"><span id="mandate-tax-result">--</span> PLN</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="result-item p-3 border rounded">
                    <h6 class="text-muted mb-2">Koszt pracodawcy</h6>
                    <div class="h5 mb-0"><span id="mandate-employer-cost">--</span> PLN</div>
                  </div>
                </div>
              </div>

              <div class="mt-4">
                <h5 class="mb-3">Szczegółowy podział kosztów umowy zlecenie</h5>
                <div class="chart-container" style="height: 300px;">
                  <canvas id="mandateChart"></canvas>
                </div>
              </div>

              <div class="mt-4 d-flex justify-content-between flex-wrap gap-2">
                <button id="mandate-recalculate-btn" class="btn btn-outline-secondary">
                  <i class="fas fa-redo me-2"></i>Przelicz ponownie
                </button>
                <div>
                  <button id="mandate-share-results" class="btn btn-outline-primary me-2">
                    <i class="fas fa-share-alt me-2"></i>Udostępnij
                  </button>
                  <button id="mandate-print-results" class="btn btn-outline-success">
                    <i class="fas fa-print me-2"></i>Drukuj
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- 3W Content Section - Co to jest Umowa Zlecenie -->
<section class="py-5 bg-white" id="poradnik">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="fw-bold mb-4">Co to jest Umowa Zlecenie?</h2>
        <p class="lead mb-4"><strong>Umowa zlecenie</strong> to jeden z najpopularniejszych typów umów cywilnoprawnych w Polsce, regulowany przez Kodeks Cywilny. <strong>Kalkulator umowy zlecenie</strong> pomaga precyzyjnie obliczyć wszystkie koszty i wynagrodzenie netto związane z tego typu umową w 2025 roku.</p>

        <p class="mb-4"><strong>Umowa zlecenie 2025</strong> charakteryzuje się:</p>
        <ul class="list-unstyled mb-4">
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Obowiązkiem płacenia składek ZUS (emerytalne, rentowe, wypadkowe)</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Podatkiem dochodowym według skali podatkowej</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Możliwością dobrowolnego ubezpieczenia chorobowego</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Brakiem ochrony Kodeksu Pracy</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Elastycznością w zakresie czasu pracy</li>
        </ul>

        <div class="alert alert-info mb-4">
          <h5 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Ważne w 2025 roku</h5>
          <p class="mb-0">Minimalne wynagrodzenie dla umowy zlecenie jako głównego źródła dochodu wynosi <strong>4666 PLN brutto miesięcznie</strong>. Nasz <strong>kalkulator umowy zlecenie</strong> automatycznie uwzględnia te przepisy.</p>
        </div>

        <h2 class="fw-bold mb-4 mt-5">Dlaczego Warto Używać Kalkulatora Umowy Zlecenie?</h2>
        <p class="mb-4">Nasz <strong>kalkulator umowy zlecenie 2025</strong> oferuje kompleksowe obliczenia z uwzględnieniem wszystkich aktualnych przepisów:</p>

        <div class="row g-4 mb-4">
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <i class="fas fa-calculator"></i>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Precyzyjne Obliczenia</h5>
                <p class="mb-0">Uwzględnia wszystkie składki ZUS obowiązujące w 2025 roku</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <i class="fas fa-shield-alt"></i>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Zgodność z Prawem</h5>
                <p class="mb-0">Aktualne stawki podatków i składek na 2025 rok</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <i class="fas fa-chart-pie"></i>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Wizualizacja Kosztów</h5>
                <p class="mb-0">Przejrzyste wykresy podziału wynagrodzeń i składek</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <i class="fas fa-mobile-alt"></i>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Dostępność</h5>
                <p class="mb-0">Działa na wszystkich urządzeniach - komputer, tablet, telefon</p>
              </div>
            </div>
          </div>
        </div>

        <h2 class="fw-bold mb-4 mt-5">Jak Obliczyć Umowę Zlecenie - Krok po Kroku</h2>
        <p class="lead mb-4">Korzystanie z <strong>kalkulatora umowy zlecenie</strong> jest bardzo proste i intuicyjne:</p>

        <div class="row g-4">
          <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body p-4">
                <div class="text-primary mb-3">
                  <i class="fas fa-edit fa-2x"></i>
                </div>
                <h5 class="card-title">1. Wprowadź kwotę</h5>
                <p class="card-text">Podaj wynagrodzenie brutto lub netto dla umowy zlecenie</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body p-4">
                <div class="text-primary mb-3">
                  <i class="fas fa-user-check fa-2x"></i>
                </div>
                <h5 class="card-title">2. Określ status</h5>
                <p class="card-text">Zaznacz czy to główne źródło dochodu i ubezpieczenie chorobowe</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body p-4">
                <div class="text-primary mb-3">
                  <i class="fas fa-calculator fa-2x"></i>
                </div>
                <h5 class="card-title">3. Oblicz wyniki</h5>
                <p class="card-text">Otrzymaj szczegółowe wyniki z podziałem na składki i podatki</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body p-4">
                <div class="text-primary mb-3">
                  <i class="fas fa-chart-bar fa-2x"></i>
                </div>
                <h5 class="card-title">4. Analizuj wykres</h5>
                <p class="card-text">Zobacz wizualny podział kosztów umowy zlecenie</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Składki ZUS dla Umowy Zlecenie Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Składki ZUS dla Umowy Zlecenie 2025</h2>
      <p class="lead">Nasz <strong>kalkulator umowy zlecenie</strong> uwzględnia wszystkie obowiązujące składki ZUS na 2025 rok</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="card shadow-sm">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-table me-2"></i>Składki ZUS od Umowy Zlecenie 2025</h5>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped mb-0">
                <thead>
                  <tr>
                    <th>Rodzaj składki</th>
                    <th>Stawka</th>
                    <th>Podstawa wymiaru</th>
                    <th>Obowiązkowa</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><strong>Emerytalna</strong></td>
                    <td><span class="badge bg-primary">9,76%</span></td>
                    <td>Wynagrodzenie brutto</td>
                    <td><i class="fas fa-check text-success"></i> Tak</td>
                  </tr>
                  <tr>
                    <td><strong>Rentowa</strong></td>
                    <td><span class="badge bg-primary">1,5%</span></td>
                    <td>Wynagrodzenie brutto</td>
                    <td><i class="fas fa-check text-success"></i> Tak</td>
                  </tr>
                  <tr>
                    <td><strong>Wypadkowa</strong></td>
                    <td><span class="badge bg-primary">1,67%</span></td>
                    <td>Wynagrodzenie brutto</td>
                    <td><i class="fas fa-check text-success"></i> Tak</td>
                  </tr>
                  <tr>
                    <td><strong>Chorobowa</strong></td>
                    <td><span class="badge bg-warning">2,45%</span></td>
                    <td>Wynagrodzenie brutto</td>
                    <td><i class="fas fa-times text-danger"></i> Dobrowolna</td>
                  </tr>
                  <tr class="table-info">
                    <td><strong>RAZEM (obowiązkowe)</strong></td>
                    <td><span class="badge bg-success">12,93%</span></td>
                    <td>-</td>
                    <td>-</td>
                  </tr>
                  <tr class="table-warning">
                    <td><strong>RAZEM (z chorobową)</strong></td>
                    <td><span class="badge bg-info">15,38%</span></td>
                    <td>-</td>
                    <td>-</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div class="mt-4">
              <div class="alert alert-warning">
                <h6 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>Ważne informacje o składkach ZUS</h6>
                <ul class="mb-0">
                  <li>Składki płaci zleceniobiorca (pracownik)</li>
                  <li>Składka chorobowa jest dobrowolna - można z niej zrezygnować</li>
                  <li>Jeśli umowa zlecenie to główne źródło dochodu, obowiązuje minimalna podstawa wymiaru składek</li>
                  <li>Nasz <strong>kalkulator umowy zlecenie</strong> automatycznie uwzględnia wszystkie te zasady</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Podatek Dochodowy Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="fw-bold mb-4">Podatek Dochodowy od Umowy Zlecenie</h2>
        <p class="lead mb-4">Wynagrodzenie z <strong>umowy zlecenie</strong> podlega opodatkowaniu podatkiem dochodowym według skali podatkowej. <strong>Kalkulator umowy zlecenie</strong> automatycznie oblicza należny podatek.</p>

        <div class="row g-4 mb-4">
          <div class="col-md-6">
            <div class="card border-primary">
              <div class="card-header bg-primary text-white">
                <h5 class="mb-0">I próg podatkowy</h5>
              </div>
              <div class="card-body">
                <h3 class="text-primary">12%</h3>
                <p class="mb-0">Dochody do <strong>120 000 PLN</strong> rocznie</p>
                <small class="text-muted">Kwota wolna od podatku: 30 000 PLN</small>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card border-danger">
              <div class="card-header bg-danger text-white">
                <h5 class="mb-0">II próg podatkowy</h5>
              </div>
              <div class="card-body">
                <h3 class="text-danger">32%</h3>
                <p class="mb-0">Dochody powyżej <strong>120 000 PLN</strong> rocznie</p>
                <small class="text-muted">Podatek: 10 800 PLN + 32% nadwyżki</small>
              </div>
            </div>
          </div>
        </div>

        <div class="alert alert-info mb-4">
          <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Koszty uzyskania przychodu</h6>
          <p class="mb-0">Od umowy zlecenie można odliczyć koszty uzyskania przychodu w wysokości <strong>20%</strong> przychodu, ale nie więcej niż <strong>30 000 PLN rocznie</strong>. Nasz <strong>kalkulator umowy zlecenie</strong> uwzględnia te odliczenia.</p>
        </div>

        <h2 class="fw-bold mb-4 mt-5">Umowa Zlecenie vs Umowa o Pracę - Porównanie</h2>
        <p class="mb-4">Porównanie najważniejszych różnic między umową zlecenie a umową o pracę:</p>

        <div class="table-responsive">
          <table class="table table-bordered">
            <thead class="table-dark">
              <tr>
                <th>Aspekt</th>
                <th>Umowa Zlecenie</th>
                <th>Umowa o Pracę</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><strong>Składki ZUS</strong></td>
                <td>12,93% (bez chorobowej)<br>15,38% (z chorobową)</td>
                <td>13,71% (pracownik)<br>19,48-22,41% (pracodawca)</td>
              </tr>
              <tr>
                <td><strong>Podatek dochodowy</strong></td>
                <td>12% lub 32% (skala podatkowa)</td>
                <td>12% lub 32% (skala podatkowa)</td>
              </tr>
              <tr>
                <td><strong>Koszty uzyskania</strong></td>
                <td>20% (max 30 000 PLN/rok)</td>
                <td>250 PLN miesięcznie</td>
              </tr>
              <tr>
                <td><strong>Ochrona prawna</strong></td>
                <td>Kodeks Cywilny</td>
                <td>Kodeks Pracy</td>
              </tr>
              <tr>
                <td><strong>Urlop</strong></td>
                <td>Brak</td>
                <td>26 dni (minimum)</td>
              </tr>
              <tr>
                <td><strong>Wypowiedzenie</strong></td>
                <td>Według umowy</td>
                <td>Okresy wypowiedzenia</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="mt-4 p-4 bg-light rounded">
          <h5 class="text-primary mb-3"><i class="fas fa-lightbulb me-2"></i>Kiedy wybrać umowę zlecenie?</h5>
          <ul class="mb-0">
            <li>Gdy potrzebujesz elastyczności w pracy</li>
            <li>Dla projektów krótkoterminowych</li>
            <li>Gdy chcesz odliczać wyższe koszty uzyskania przychodu</li>
            <li>Dla dodatkowego źródła dochodu</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Często Zadawane Pytania o Umowę Zlecenie</h2>
      <p class="lead">Najważniejsze informacje o umowach zlecenie i naszym kalkulatorze</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="accordion" id="faqAccordion">
          <div class="accordion-item">
            <h3 class="accordion-header" id="faq1">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                Jakie składki ZUS płaci się od umowy zlecenie?
              </button>
            </h3>
            <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                Od <strong>umowy zlecenie</strong> płaci się obowiązkowe składki: emerytalne (9,76%), rentowe (1,5%) oraz wypadkowe (1,67%). Składka chorobowa (2,45%) jest dobrowolna. Nasz <strong>kalkulator umowy zlecenie</strong> uwzględnia wszystkie te składki.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header" id="faq2">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                Czy od umowy zlecenie płaci się podatek dochodowy?
              </button>
            </h3>
            <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                Tak, wynagrodzenie z <strong>umowy zlecenie</strong> podlega opodatkowaniu podatkiem dochodowym według skali podatkowej (12% lub 32%). Można odliczyć koszty uzyskania przychodu w wysokości 20% przychodu, maksymalnie 30 000 PLN rocznie.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header" id="faq3">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                Jaka jest minimalna kwota umowy zlecenie w 2025?
              </button>
            </h3>
            <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                Jeśli <strong>umowa zlecenie</strong> jest głównym źródłem dochodu, minimalne wynagrodzenie wynosi 4666 PLN brutto miesięcznie w 2025 roku. Nasz <strong>kalkulator umowy zlecenie</strong> automatycznie sprawdza te limity.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header" id="faq4">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                Czy kalkulator umowy zlecenie jest darmowy?
              </button>
            </h3>
            <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                Tak, nasz <strong>kalkulator umowy zlecenie</strong> jest całkowicie darmowy i nie wymaga rejestracji. Możesz korzystać z niego bez ograniczeń, obliczając wynagrodzenia dla różnych kwot i scenariuszy.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header" id="faq5">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse5">
                Czy można mieć kilka umów zlecenie jednocześnie?
              </button>
            </h3>
            <div id="collapse5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                Tak, można mieć kilka <strong>umów zlecenie</strong> jednocześnie. Składki ZUS płaci się od każdej umowy osobno, ale podatek dochodowy rozlicza się łącznie ze wszystkich źródeł dochodu. Użyj naszego <strong>kalkulatora umowy zlecenie</strong> dla każdej umowy osobno.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Related Calculators Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Powiązane Kalkulatory Wynagrodzeń</h2>
      <p class="lead">Sprawdź inne kalkulatory dostosowane do różnych typów umów</p>
    </div>

    <div class="row g-4">
      <div class="col-md-6 col-lg-4">
        <div class="card h-100 shadow-sm border-0">
          <div class="card-body p-4">
            <div class="text-primary mb-3">
              <i class="fas fa-briefcase fa-2x"></i>
            </div>
            <h3 class="h5 mb-3">Kalkulator Brutto-Netto</h3>
            <p class="card-text">Oblicz wynagrodzenie dla umowy o pracę z uwzględnieniem wszystkich składek i podatków na 2025 rok.</p>
            <div class="mt-auto pt-3">
              <a href="/kalkulator-brutto-netto-2025" class="btn btn-outline-primary d-block">Oblicz Umowę o Pracę</a>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-4">
        <div class="card h-100 shadow-sm border-0">
          <div class="card-body p-4">
            <div class="text-primary mb-3">
              <i class="fas fa-chart-line fa-2x"></i>
            </div>
            <h3 class="h5 mb-3">Kalkulator B2B</h3>
            <p class="card-text">Specjalistyczny kalkulator dla przedsiębiorców rozliczających się na zasadach ogólnych lub ryczałcie.</p>
            <div class="mt-auto pt-3">
              <a href="/kalkulator-b2b" class="btn btn-outline-primary d-block">Oblicz B2B</a>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-4">
        <div class="card h-100 shadow-sm border-0">
          <div class="card-body p-4">
            <div class="text-primary mb-3">
              <i class="fas fa-home fa-2x"></i>
            </div>
            <h3 class="h5 mb-3">Wszystkie Kalkulatory</h3>
            <p class="card-text">Wróć do strony głównej i sprawdź wszystkie dostępne kalkulatory wynagrodzeń na 2025 rok.</p>
            <div class="mt-auto pt-3">
              <a href="/" class="btn btn-outline-primary d-block">Strona Główna</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('mandate-calculator');
    const resultsSection = document.getElementById('mandate-results');
    let salaryChart = null;

    // Stawki na 2025 rok
    const rates = {
        pension: 0.0976,      // emerytalna
        disability: 0.015,    // rentowa
        accident: 0.0167,     // wypadkowa
        sickness: 0.0245,     // chorobowa (dobrowolna)
        taxThreshold: 120000, // próg podatkowy
        taxRate1: 0.12,       // I próg
        taxRate2: 0.32,       // II próg
        taxFree: 30000,       // kwota wolna
        costDeduction: 0.20,  // koszty uzyskania (20%)
        maxCostDeduction: 30000, // max koszty uzyskania rocznie
        minWage: 4666         // minimalne wynagrodzenie 2025
    };

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        calculateMandateContract();
    });

    document.getElementById('mandate-recalculate-btn').addEventListener('click', function() {
        resultsSection.style.display = 'none';
        form.scrollIntoView({ behavior: 'smooth' });
    });

    function calculateMandateContract() {
        const amount = parseFloat(document.getElementById('mandate-amount').value);
        const amountType = document.querySelector('input[name="mandate-amount-type"]:checked').value;
        const age = parseInt(document.getElementById('mandate-age').value) || 26;
        const isMainJob = document.getElementById('mandate-main-job').checked;
        const hasHealthInsurance = document.getElementById('mandate-health-insurance').checked;

        if (!amount || amount <= 0) {
            alert('Proszę wprowadzić prawidłową kwotę wynagrodzenia.');
            return;
        }

        let grossAmount, netAmount;

        if (amountType === 'gross') {
            grossAmount = amount;
            netAmount = calculateNetFromGross(grossAmount, age, isMainJob, hasHealthInsurance);
        } else {
            netAmount = amount;
            grossAmount = calculateGrossFromNet(netAmount, age, isMainJob, hasHealthInsurance);
        }

        // Sprawdź minimalne wynagrodzenie
        if (isMainJob && grossAmount < rates.minWage) {
            alert(`Uwaga: Minimalne wynagrodzenie dla głównego źródła dochodu wynosi ${rates.minWage} PLN brutto.`);
        }

        const zusContributions = calculateZUSContributions(grossAmount, hasHealthInsurance);
        const taxableIncome = grossAmount - zusContributions.total;
        const costDeduction = Math.min(grossAmount * rates.costDeduction, rates.maxCostDeduction / 12);
        const taxBase = Math.max(0, taxableIncome - costDeduction);
        const incomeTax = calculateIncomeTax(taxBase * 12) / 12; // miesięczny podatek
        const employerCost = grossAmount + (grossAmount * rates.accident); // koszt pracodawcy

        displayResults({
            gross: grossAmount,
            net: netAmount,
            zus: zusContributions.total,
            tax: incomeTax,
            employerCost: employerCost,
            zusBreakdown: zusContributions
        });

        createChart({
            gross: grossAmount,
            zus: zusContributions.total,
            tax: incomeTax,
            net: netAmount
        });

        resultsSection.style.display = 'block';
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    function calculateZUSContributions(grossAmount, hasHealthInsurance) {
        const pension = grossAmount * rates.pension;
        const disability = grossAmount * rates.disability;
        const accident = grossAmount * rates.accident;
        const sickness = hasHealthInsurance ? grossAmount * rates.sickness : 0;

        return {
            pension: pension,
            disability: disability,
            accident: accident,
            sickness: sickness,
            total: pension + disability + accident + sickness
        };
    }

    function calculateIncomeTax(annualIncome) {
        const taxableIncome = Math.max(0, annualIncome - rates.taxFree);

        if (taxableIncome <= rates.taxThreshold) {
            return taxableIncome * rates.taxRate1;
        } else {
            return rates.taxThreshold * rates.taxRate1 + (taxableIncome - rates.taxThreshold) * rates.taxRate2;
        }
    }

    function calculateNetFromGross(gross, age, isMainJob, hasHealthInsurance) {
        const zusContributions = calculateZUSContributions(gross, hasHealthInsurance);
        const taxableIncome = gross - zusContributions.total;
        const costDeduction = Math.min(gross * rates.costDeduction, rates.maxCostDeduction / 12);
        const taxBase = Math.max(0, taxableIncome - costDeduction);
        const incomeTax = calculateIncomeTax(taxBase * 12) / 12;

        return gross - zusContributions.total - incomeTax;
    }

    function calculateGrossFromNet(net, age, isMainJob, hasHealthInsurance) {
        // Iteracyjne obliczanie brutto z netto (przybliżenie)
        let gross = net * 1.4; // początkowe przybliżenie
        let iterations = 0;
        const maxIterations = 20;

        while (iterations < maxIterations) {
            const calculatedNet = calculateNetFromGross(gross, age, isMainJob, hasHealthInsurance);
            const difference = net - calculatedNet;

            if (Math.abs(difference) < 0.01) break;

            gross += difference * 1.3; // korekta
            iterations++;
        }

        return gross;
    }

    function displayResults(results) {
        document.getElementById('mandate-gross-result').textContent = results.gross.toFixed(2);
        document.getElementById('mandate-net-result').textContent = results.net.toFixed(2);
        document.getElementById('mandate-zus-result').textContent = results.zus.toFixed(2);
        document.getElementById('mandate-tax-result').textContent = results.tax.toFixed(2);
        document.getElementById('mandate-employer-cost').textContent = results.employerCost.toFixed(2);
    }

    function createChart(data) {
        const ctx = document.getElementById('mandateChart').getContext('2d');

        if (salaryChart) {
            salaryChart.destroy();
        }

        salaryChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Wynagrodzenie netto', 'Składki ZUS', 'Podatek dochodowy'],
                datasets: [{
                    data: [data.net, data.zus, data.tax],
                    backgroundColor: [
                        '#28a745',
                        '#007bff',
                        '#dc3545'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const percentage = ((value / data.gross) * 100).toFixed(1);
                                return context.label + ': ' + value.toFixed(2) + ' PLN (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }

    // Funkcje udostępniania i drukowania
    document.getElementById('mandate-share-results').addEventListener('click', function() {
        const url = window.location.href;
        const text = 'Sprawdź kalkulator umowy zlecenie 2025 - oblicz wynagrodzenie brutto netto!';

        if (navigator.share) {
            navigator.share({
                title: 'Kalkulator Umowy Zlecenie 2025',
                text: text,
                url: url
            });
        } else {
            // Fallback - kopiuj do schowka
            navigator.clipboard.writeText(url + ' - ' + text).then(function() {
                alert('Link został skopiowany do schowka!');
            });
        }
    });

    document.getElementById('mandate-print-results').addEventListener('click', function() {
        window.print();
    });
});
</script>
{% endblock %}
