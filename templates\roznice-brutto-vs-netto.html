{% extends 'base.html' %}

{% block head %}
<!-- Chart.js for visualization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block title %}R<PERSON><PERSON><PERSON><PERSON>rutto vs Netto 2025 | Kompletny Przewodnik | Kalkulator Wynagrodzeń{% endblock %}

{% block description %}✅ Poznaj różnice między wynagrodzeniem brutto a netto w 2025. Szczegółowy przewodnik z przykładami, tabelami i kalkulatorem. Sprawdź teraz!{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active" aria-current="page"><PERSON><PERSON><PERSON><PERSON><PERSON> Brutto vs Netto</li>
{% endblock %}

{% block schema_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "<PERSON><PERSON><PERSON><PERSON><PERSON> Bru<PERSON> vs Netto 2025 - Kompletny Przewodnik",
  "description": "Poznaj różnice między wynagrodzeniem brutto a netto w 2025. Szczegółowy przewodnik z przykładami, tabelami i kalkulatorem.",
  "author": {
    "@type": "Organization",
    "name": "Kalkulator Wynagrodzeń",
    "url": "{{ request.url_root }}"
  },
  "publisher": {
    "@type": "Organization",
    "name": "Kalkulator Wynagrodzeń",
    "url": "{{ request.url_root }}"
  },
  "datePublished": "2025-01-01",
  "dateModified": "2025-01-01",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "{{ request.url }}"
  },
  "articleSection": "Poradniki",
  "keywords": "brutto netto różnice, wynagrodzenie brutto netto, składki ZUS, podatek dochodowy, kalkulator wynagrodzeń"
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "Czym różni się wynagrodzenie brutto od netto?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Wynagrodzenie brutto to pełna kwota przed odliczeniem składek ZUS i podatku dochodowego. Wynagrodzenie netto to kwota, którą pracownik otrzymuje na konto po odliczeniu wszystkich obowiązkowych składek i podatków."
      }
    },
    {
      "@type": "Question",
      "name": "Jakie składki są odliczane od wynagrodzenia brutto?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Od wynagrodzenia brutto odliczane są składki ZUS: emerytalna (9,76%), rentowa (1,5%), chorobowa (2,45%), wypadkowa (0,67-3,33%), oraz składka zdrowotna (9%) i podatek dochodowy (12% lub 32%)."
      }
    }
  ]
}
</script>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section bg-primary text-white text-center py-5">
  <div class="container">
    <h1 class="display-4 fw-bold mb-4">Różnice Brutto vs Netto 2025 - Kompletny Przewodnik</h1>
    <p class="lead mb-5">Poznaj szczegółowe różnice między wynagrodzeniem brutto a netto. Dowiedz się, jakie składki i podatki wpływają na Twoją pensję w 2025 roku.</p>
    <div class="d-flex justify-content-center gap-3 flex-wrap">
      <a href="#kalkulator" class="btn btn-light btn-lg px-4 py-2">
        <i class="fas fa-calculator me-2"></i>Oblicz Różnicę
      </a>
      <a href="#przewodnik" class="btn btn-outline-light btn-lg px-4 py-2">
        <i class="fas fa-book me-2"></i>Czytaj Przewodnik
      </a>
    </div>
  </div>
</section>

<!-- Quick Calculator Section -->
<section id="kalkulator" class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Kalkulator Różnic Brutto vs Netto</h2>
      <p class="lead">Sprawdź szybko, ile wynosi różnica między Twoim wynagrodzeniem brutto a netto</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="card shadow-sm">
          <div class="card-body p-4">
            <form id="brutto-netto-calculator">
              <div class="row g-3 mb-4">
                <div class="col-md-6">
                  <label for="salary-input" class="form-label fw-bold">Wynagrodzenie brutto</label>
                  <div class="input-group">
                    <input type="number" id="salary-input" class="form-control" placeholder="5000" min="0" step="0.01" required>
                    <span class="input-group-text">PLN</span>
                  </div>
                </div>
                <div class="col-md-6">
                  <label for="age-input" class="form-label fw-bold">Wiek</label>
                  <div class="input-group">
                    <input type="number" id="age-input" class="form-control" placeholder="26" min="18" max="100" value="26">
                    <span class="input-group-text">lat</span>
                  </div>
                </div>
              </div>

              <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg px-5 py-3">
                  <i class="fas fa-calculator me-2"></i>OBLICZ RÓŻNICĘ
                </button>
              </div>
            </form>

            <!-- Results -->
            <div id="calculation-results" class="mt-4" style="display: none;">
              <div class="row g-4">
                <div class="col-md-6">
                  <div class="result-card p-4 bg-primary text-white rounded">
                    <h5 class="mb-2">Wynagrodzenie Brutto</h5>
                    <div class="h3 mb-0"><span id="brutto-amount">--</span> PLN</div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="result-card p-4 bg-success text-white rounded">
                    <h5 class="mb-2">Wynagrodzenie Netto</h5>
                    <div class="h3 mb-0"><span id="netto-amount">--</span> PLN</div>
                  </div>
                </div>
              </div>

              <div class="mt-4">
                <h5 class="mb-3">Szczegółowy podział odliczeń</h5>
                <div class="chart-container" style="height: 300px;">
                  <canvas id="deductionsChart"></canvas>
                </div>
              </div>

              <div class="mt-4">
                <div class="alert alert-info">
                  <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Różnica Brutto vs Netto</h6>
                  <p class="mb-0">Różnica między Twoim wynagrodzeniem brutto a netto wynosi <strong><span id="difference-amount">--</span> PLN</strong>, co stanowi <strong><span id="difference-percentage">--</span>%</strong> Twojego wynagrodzenia brutto.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Main Content Section -->
<section class="py-5 bg-white" id="przewodnik">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="fw-bold mb-4">Czym Różni się Wynagrodzenie Brutto od Netto?</h2>
        
        <p class="lead mb-4">Różnica między <strong>wynagrodzeniem brutto a netto</strong> to jeden z najważniejszych aspektów, które każdy pracownik powinien rozumieć. <strong>Wynagrodzenie brutto</strong> to pełna kwota, którą pracodawca zobowiązuje się wypłacić pracownikowi, natomiast <strong>wynagrodzenie netto</strong> to rzeczywista kwota, która trafia na konto pracownika po odliczeniu wszystkich obowiązkowych składek i podatków.</p>

        <div class="row g-4 mb-5">
          <div class="col-md-6">
            <div class="card border-primary h-100">
              <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Wynagrodzenie Brutto</h4>
              </div>
              <div class="card-body">
                <p class="mb-3"><strong>Wynagrodzenie brutto</strong> to:</p>
                <ul class="list-unstyled">
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Pełna kwota przed odliczeniami</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Podstawa do naliczania składek ZUS</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Kwota określona w umowie o pracę</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Podstawa do obliczania podatku</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div class="col-md-6">
            <div class="card border-success h-100">
              <div class="card-header bg-success text-white">
                <h4 class="mb-0"><i class="fas fa-hand-holding-usd me-2"></i>Wynagrodzenie Netto</h4>
              </div>
              <div class="card-body">
                <p class="mb-3"><strong>Wynagrodzenie netto</strong> to:</p>
                <ul class="list-unstyled">
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Kwota po odliczeniu składek ZUS</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Kwota po odliczeniu podatku</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Rzeczywista kwota na koncie</li>
                  <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Kwota do dyspozycji pracownika</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <h2 class="fw-bold mb-4">Jakie Składki i Podatki Wpływają na Różnicę Brutto vs Netto?</h2>
        
        <p class="mb-4">Różnica między <strong>wynagrodzeniem brutto a netto</strong> wynika z obowiązkowych odliczeń, które pracodawca musi potrącić z wynagrodzenia brutto. W 2025 roku obowiązują następujące stawki składek i podatków:</p>

        <div class="card shadow-sm mb-5">
          <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="fas fa-table me-2"></i>Składki ZUS 2025 - Odliczane od Wynagrodzenia Brutto</h5>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped mb-0">
                <thead>
                  <tr>
                    <th>Rodzaj składki</th>
                    <th>Stawka</th>
                    <th>Przykład (5000 PLN brutto)</th>
                    <th>Opis</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><strong>Emerytalna</strong></td>
                    <td><span class="badge bg-primary">9,76%</span></td>
                    <td>488,00 PLN</td>
                    <td>Składka na przyszłą emeryturę</td>
                  </tr>
                  <tr>
                    <td><strong>Rentowa</strong></td>
                    <td><span class="badge bg-primary">1,5%</span></td>
                    <td>75,00 PLN</td>
                    <td>Składka na rentę z tytułu niezdolności do pracy</td>
                  </tr>
                  <tr>
                    <td><strong>Chorobowa</strong></td>
                    <td><span class="badge bg-primary">2,45%</span></td>
                    <td>122,50 PLN</td>
                    <td>Składka na zasiłek chorobowy</td>
                  </tr>
                  <tr>
                    <td><strong>Wypadkowa</strong></td>
                    <td><span class="badge bg-warning">0,67% - 3,33%</span></td>
                    <td>33,50 PLN</td>
                    <td>Zależna od branży i ryzyka zawodowego</td>
                  </tr>
                  <tr class="table-info">
                    <td><strong>Razem składki ZUS</strong></td>
                    <td><strong>13,71% - 16,04%</strong></td>
                    <td><strong>719,00 PLN</strong></td>
                    <td>Łączne składki społeczne</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <h2 class="fw-bold mb-4">Składka Zdrowotna i Podatek Dochodowy</h2>

        <p class="mb-4">Po odliczeniu składek ZUS od wynagrodzenia brutto, otrzymujemy podstawę opodatkowania. Od tej kwoty naliczane są kolejne obowiązkowe składki:</p>

        <div class="row g-4 mb-5">
          <div class="col-md-6">
            <div class="card border-info h-100">
              <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-heartbeat me-2"></i>Składka Zdrowotna</h5>
              </div>
              <div class="card-body">
                <ul class="list-unstyled">
                  <li class="mb-2"><strong>Stawka:</strong> 9% podstawy opodatkowania</li>
                  <li class="mb-2"><strong>Do odliczenia od podatku:</strong> 7,75%</li>
                  <li class="mb-2"><strong>Przykład (4281 PLN podstawa):</strong> 385,29 PLN</li>
                  <li class="mb-2"><strong>Cel:</strong> Finansowanie ochrony zdrowia</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="card border-danger h-100">
              <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="fas fa-percentage me-2"></i>Podatek Dochodowy</h5>
              </div>
              <div class="card-body">
                <ul class="list-unstyled">
                  <li class="mb-2"><strong>I próg:</strong> 12% (do 120 000 PLN rocznie)</li>
                  <li class="mb-2"><strong>II próg:</strong> 32% (powyżej 120 000 PLN)</li>
                  <li class="mb-2"><strong>Kwota wolna:</strong> 30 000 PLN rocznie</li>
                  <li class="mb-2"><strong>Ulga dla młodych:</strong> Do 26. roku życia</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <h2 class="fw-bold mb-4">Praktyczne Przykłady Różnic Brutto vs Netto</h2>

        <p class="mb-4">Aby lepiej zrozumieć <strong>różnice między wynagrodzeniem brutto a netto</strong>, przeanalizujmy kilka praktycznych przykładów dla różnych poziomów wynagrodzeń w 2025 roku:</p>

        <div class="card shadow-sm mb-5">
          <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Przykłady Obliczeń Brutto vs Netto 2025</h5>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th>Wynagrodzenie Brutto</th>
                    <th>Składki ZUS</th>
                    <th>Składka Zdrowotna</th>
                    <th>Podatek</th>
                    <th>Wynagrodzenie Netto</th>
                    <th>Różnica</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><strong>3 000 PLN</strong></td>
                    <td>431,40 PLN</td>
                    <td>231,17 PLN</td>
                    <td>8,23 PLN</td>
                    <td><strong>2 329,20 PLN</strong></td>
                    <td class="text-danger">670,80 PLN (22,4%)</td>
                  </tr>
                  <tr>
                    <td><strong>5 000 PLN</strong></td>
                    <td>719,00 PLN</td>
                    <td>385,29 PLN</td>
                    <td>181,73 PLN</td>
                    <td><strong>3 713,98 PLN</strong></td>
                    <td class="text-danger">1 286,02 PLN (25,7%)</td>
                  </tr>
                  <tr>
                    <td><strong>8 000 PLN</strong></td>
                    <td>1 150,40 PLN</td>
                    <td>616,46 PLN</td>
                    <td>521,87 PLN</td>
                    <td><strong>5 711,27 PLN</strong></td>
                    <td class="text-danger">2 288,73 PLN (28,6%)</td>
                  </tr>
                  <tr>
                    <td><strong>12 000 PLN</strong></td>
                    <td>1 725,60 PLN</td>
                    <td>924,69 PLN</td>
                    <td>1 032,81 PLN</td>
                    <td><strong>8 316,90 PLN</strong></td>
                    <td class="text-danger">3 683,10 PLN (30,7%)</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="mt-3">
              <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                Obliczenia dla osoby powyżej 26. roku życia, bez ulg podatkowych, stawka wypadkowa 0,67%
              </small>
            </div>
          </div>
        </div>

        <h2 class="fw-bold mb-4">Czynniki Wpływające na Różnicę Brutto vs Netto</h2>

        <p class="mb-4">Różnica między <strong>wynagrodzeniem brutto a netto</strong> może się różnić w zależności od kilku kluczowych czynników:</p>

        <div class="row g-4 mb-5">
          <div class="col-md-6 col-lg-3">
            <div class="text-center p-4 border rounded h-100">
              <div class="text-primary mb-3">
                <i class="fas fa-birthday-cake fa-2x"></i>
              </div>
              <h5>Wiek Pracownika</h5>
              <p class="small mb-0">Osoby do 26. roku życia korzystają z ulgi podatkowej - nie płacą podatku dochodowego do kwoty 85 528 PLN rocznie</p>
            </div>
          </div>

          <div class="col-md-6 col-lg-3">
            <div class="text-center p-4 border rounded h-100">
              <div class="text-primary mb-3">
                <i class="fas fa-industry fa-2x"></i>
              </div>
              <h5>Branża</h5>
              <p class="small mb-0">Stawka składki wypadkowej zależy od branży i wynosi od 0,67% do 3,33% wynagrodzenia brutto</p>
            </div>
          </div>

          <div class="col-md-6 col-lg-3">
            <div class="text-center p-4 border rounded h-100">
              <div class="text-primary mb-3">
                <i class="fas fa-piggy-bank fa-2x"></i>
              </div>
              <h5>PPK</h5>
              <p class="small mb-0">Uczestnictwo w PPK oznacza dodatkowe 2% składki pracownika od wynagrodzenia brutto</p>
            </div>
          </div>

          <div class="col-md-6 col-lg-3">
            <div class="text-center p-4 border rounded h-100">
              <div class="text-primary mb-3">
                <i class="fas fa-file-invoice-dollar fa-2x"></i>
              </div>
              <h5>Ulgi Podatkowe</h5>
              <p class="small mb-0">Różne ulgi podatkowe mogą znacząco zmniejszyć podatek dochodowy i zwiększyć wynagrodzenie netto</p>
            </div>
          </div>
        </div>

        <h2 class="fw-bold mb-4">Jak Zwiększyć Wynagrodzenie Netto?</h2>

        <p class="mb-4">Istnieje kilka legalnych sposobów na zwiększenie <strong>wynagrodzenia netto</strong> przy tym samym wynagrodzeniu brutto:</p>

        <div class="accordion" id="nettoTipsAccordion">
          <div class="accordion-item">
            <h3 class="accordion-header" id="headingOne">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                <i class="fas fa-gift me-2"></i>Świadczenia Pozapłacowe
              </button>
            </h3>
            <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#nettoTipsAccordion">
              <div class="accordion-body">
                <p>Pracodawca może oferować różne świadczenia pozapłacowe, które nie podlegają opodatkowaniu lub są opodatkowane preferencyjnie:</p>
                <ul>
                  <li><strong>Karta Multisport</strong> - do 600 PLN rocznie zwolnione z podatku</li>
                  <li><strong>Prywatna opieka medyczna</strong> - zwolniona z podatku</li>
                  <li><strong>Dofinansowanie do wypoczynku</strong> - do 2280 PLN rocznie</li>
                  <li><strong>Telefon służbowy</strong> - możliwość użytku prywatnego</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header" id="headingTwo">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                <i class="fas fa-percentage me-2"></i>Ulgi Podatkowe
              </button>
            </h3>
            <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#nettoTipsAccordion">
              <div class="accordion-body">
                <p>Wykorzystanie dostępnych ulg podatkowych może znacząco zmniejszyć podatek dochodowy:</p>
                <ul>
                  <li><strong>Ulga na dziecko</strong> - 1112,04 PLN rocznie na każde dziecko</li>
                  <li><strong>Ulga rehabilitacyjna</strong> - dla osób niepełnosprawnych</li>
                  <li><strong>Ulga internetowa</strong> - do 760 PLN rocznie</li>
                  <li><strong>Darowizny</strong> - do 6% dochodu na cele charytatywne</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header" id="headingThree">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                <i class="fas fa-briefcase me-2"></i>Zmiana Formy Zatrudnienia
              </button>
            </h3>
            <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#nettoTipsAccordion">
              <div class="accordion-body">
                <p>W niektórych przypadkach zmiana formy zatrudnienia może być korzystna:</p>
                <ul>
                  <li><strong>Umowa B2B</strong> - możliwość rozliczania kosztów uzyskania przychodu</li>
                  <li><strong>Umowa zlecenie</strong> - brak składki chorobowej (dla dodatkowych umów)</li>
                  <li><strong>Działalność gospodarcza</strong> - różne formy opodatkowania</li>
                </ul>
                <div class="alert alert-warning mt-3">
                  <small><i class="fas fa-exclamation-triangle me-1"></i> Zawsze skonsultuj się z księgowym przed zmianą formy zatrudnienia</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="fw-bold mb-5 text-center">Najczęściej Zadawane Pytania o Różnice Brutto vs Netto</h2>

        <div class="accordion" id="faqAccordion">
          <div class="accordion-item">
            <h3 class="accordion-header" id="faqHeading1">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse1">
                Czym różni się wynagrodzenie brutto od netto?
              </button>
            </h3>
            <div id="faqCollapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <strong>Wynagrodzenie brutto</strong> to pełna kwota przed odliczeniem składek ZUS i podatku dochodowego. <strong>Wynagrodzenie netto</strong> to kwota, którą pracownik otrzymuje na konto po odliczeniu wszystkich obowiązkowych składek i podatków. Różnica może wynosić od 20% do 35% w zależności od wysokości wynagrodzenia i sytuacji osobistej pracownika.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header" id="faqHeading2">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse2">
                Jakie składki są odliczane od wynagrodzenia brutto?
              </button>
            </h3>
            <div id="faqCollapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                Od wynagrodzenia brutto odliczane są składki ZUS: emerytalna (9,76%), rentowa (1,5%), chorobowa (2,45%), wypadkowa (0,67-3,33%), oraz składka zdrowotna (9%) i podatek dochodowy (12% lub 32%). Łącznie składki ZUS wynoszą około 13,71-16,04% wynagrodzenia brutto.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header" id="faqHeading3">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse3">
                Czy osoby do 26 lat płacą podatek dochodowy?
              </button>
            </h3>
            <div id="faqCollapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                Osoby do 26. roku życia korzystają z ulgi podatkowej i nie płacą podatku dochodowego od dochodów do 85 528 PLN rocznie (około 7 127 PLN miesięcznie brutto). Powyżej tej kwoty obowiązuje standardowa stawka 12%.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header" id="faqHeading4">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse4">
                Jak obliczyć wynagrodzenie netto z brutto?
              </button>
            </h3>
            <div id="faqCollapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                Aby obliczyć wynagrodzenie netto z brutto: 1) Odejmij składki ZUS (około 13,71%), 2) Od pozostałej kwoty odejmij składkę zdrowotną (9%), 3) Oblicz podatek dochodowy (12% lub 32% minus kwota wolna), 4) Odejmij podatek od podstawy opodatkowania. Najłatwiej skorzystać z naszego kalkulatora powyżej.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header" id="faqHeading5">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse5">
                Czy można zwiększyć wynagrodzenie netto bez zwiększania brutto?
              </button>
            </h3>
            <div id="faqCollapse5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                Tak, można zwiększyć wynagrodzenie netto poprzez: świadczenia pozapłacowe (karta Multisport, prywatna opieka medyczna), wykorzystanie ulg podatkowych (ulga na dziecko, rehabilitacyjna), optymalizację formy zatrudnienia lub skorzystanie z programów pracowniczych oferowanych przez pracodawcę.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Call to Action Section -->
<section class="py-5 bg-primary text-white">
  <div class="container text-center">
    <h2 class="fw-bold mb-4">Sprawdź Swoje Wynagrodzenie Brutto vs Netto</h2>
    <p class="lead mb-4">Skorzystaj z naszego darmowego kalkulatora wynagrodzeń i poznaj dokładną różnicę między Twoim wynagrodzeniem brutto a netto</p>
    <div class="d-flex justify-content-center gap-3 flex-wrap">
      <a href="/" class="btn btn-light btn-lg px-4 py-2">
        <i class="fas fa-calculator me-2"></i>Kalkulator Wynagrodzeń
      </a>
      <a href="/kalkulator-brutto-netto-2025" class="btn btn-outline-light btn-lg px-4 py-2">
        <i class="fas fa-chart-line me-2"></i>Kalkulator Brutto-Netto
      </a>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('brutto-netto-calculator');
    const resultsDiv = document.getElementById('calculation-results');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const bruttoSalary = parseFloat(document.getElementById('salary-input').value);
        const age = parseInt(document.getElementById('age-input').value);
        
        if (bruttoSalary && age) {
            calculateBruttoNetto(bruttoSalary, age);
            resultsDiv.style.display = 'block';
            resultsDiv.scrollIntoView({ behavior: 'smooth' });
        }
    });
    
    function calculateBruttoNetto(brutto, age) {
        // ZUS contributions
        const emerytalna = brutto * 0.0976;
        const rentowa = brutto * 0.015;
        const chorobowa = brutto * 0.0245;
        const wypadkowa = brutto * 0.0067; // average rate
        
        const totalZUS = emerytalna + rentowa + chorobowa + wypadkowa;
        const podstawaOpodatkowania = brutto - totalZUS;
        
        // Health insurance
        const zdrowotna = podstawaOpodatkowania * 0.09;
        const zdrowotnaDoPodatku = podstawaOpodatkowania * 0.0775;
        
        // Income tax
        let podatek = 0;
        if (podstawaOpodatkowania <= 120000) {
            podatek = podstawaOpodatkowania * 0.12 - 3600;
        } else {
            podatek = podstawaOpodatkowania * 0.32 - 27600;
        }
        
        if (podatek < 0) podatek = 0;
        podatek = Math.round(podatek);
        
        const netto = brutto - totalZUS - zdrowotna - podatek;
        const difference = brutto - netto;
        const differencePercentage = ((difference / brutto) * 100).toFixed(1);
        
        // Update results
        document.getElementById('brutto-amount').textContent = brutto.toFixed(2);
        document.getElementById('netto-amount').textContent = netto.toFixed(2);
        document.getElementById('difference-amount').textContent = difference.toFixed(2);
        document.getElementById('difference-percentage').textContent = differencePercentage;
        
        // Create chart
        createDeductionsChart(brutto, totalZUS, zdrowotna, podatek, netto);
    }
    
    function createDeductionsChart(brutto, zus, health, tax, netto) {
        const ctx = document.getElementById('deductionsChart').getContext('2d');
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Wynagrodzenie Netto', 'Składki ZUS', 'Składka Zdrowotna', 'Podatek Dochodowy'],
                datasets: [{
                    data: [netto, zus, health, tax],
                    backgroundColor: [
                        '#28a745',
                        '#007bff',
                        '#17a2b8',
                        '#dc3545'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const percentage = ((value / brutto) * 100).toFixed(1);
                                return context.label + ': ' + value.toFixed(2) + ' PLN (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>
{% endblock %}
